// Enhanced K6 script (converted from Postman + performance enhancements)
import http from 'k6/http';
import { check, group, sleep } from 'k6';
import { Rate, Trend } from 'k6/metrics';

// Custom metrics
let errorRate = new Rate('errors');
let apiResponseTime = new Trend('api_response_time');

// Performance testing configuration
export let options = {
  stages: [
    { duration: '2m', target: 20 },   // Ramp up
    { duration: '5m', target: 20 },   // Stay at 20 users
    { duration: '2m', target: 50 },   // Ramp up to 50
    { duration: '5m', target: 50 },   // Stay at 50 users
    { duration: '2m', target: 0 },    // Ramp down
  ],
  thresholds: {
    http_req_duration: ['p(95)<500'], // 95% under 500ms
    http_req_failed: ['rate<0.1'],    // Error rate under 10%
    errors: ['rate<0.1'],
  },
};

const BASE_URL = 'https://your-api.com';

// Your existing Postman requests (converted by K6)
export default function() {
  let headers = {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer your-token-here'
  };

  group('Authentication APIs', () => {
    // Login request (from your Postman collection)
    let loginResponse = http.post(`${BASE_URL}/auth/login`, JSON.stringify({
      username: 'testuser',
      password: 'password123'
    }), { headers });
    
    check(loginResponse, {
      'login status is 200': (r) => r.status === 200,
      'login response time < 300ms': (r) => r.timings.duration < 300,
    });
    
    errorRate.add(loginResponse.status !== 200);
    apiResponseTime.add(loginResponse.timings.duration);
  });

  group('User Management APIs', () => {
    // Get users (from your Postman collection)
    let getUsersResponse = http.get(`${BASE_URL}/api/users`, { headers });
    
    check(getUsersResponse, {
      'get users status is 200': (r) => r.status === 200,
      'get users response time < 200ms': (r) => r.timings.duration < 200,
    });
    
    errorRate.add(getUsersResponse.status !== 200);
    apiResponseTime.add(getUsersResponse.timings.duration);

    // Create user (from your Postman collection)
    let createUserResponse = http.post(`${BASE_URL}/api/users`, JSON.stringify({
      name: 'Test User',
      email: '<EMAIL>'
    }), { headers });
    
    check(createUserResponse, {
      'create user status is 201': (r) => r.status === 201,
      'create user response time < 400ms': (r) => r.timings.duration < 400,
    });
    
    errorRate.add(createUserResponse.status !== 201);
    apiResponseTime.add(createUserResponse.timings.duration);
  });

  // Add more API groups from your Postman collection...

  sleep(1); // Think time between requests
} 