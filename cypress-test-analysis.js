// Cypress test code from the image for error analysis
describe('Deposit via PayStack', () => {
  it('should deposit N7,500 and update balance', () => {
    
    cy.intercept('GET', '/api/paystack/init').as('PayInit');
    
    cy.visit('/deposit');
    cy.get('#amount').clear().type('7500');
    cy.get('#deposit_button').click();
    
    cy.wait('@initPaystack').its('response.status').should('eq', 201);
    .then(( response ) => {
      const authUrl = response.data.authorization_url;
      cy.visit(authUrl + '?data-test');
    });
    
    cy.intercept('POST', '/api/deposit/verify/payment', {
      status: 200,
      body: { status: 'OK', updatedBalance: 27500 }
    }).as('verify');
    
    cy.visit('/deposit/callback');
    cy.wait('@verify/Deposit');
    
    cy.visit('/dashboard');
    cy.get('#balance').should('contain.text', 'N7500');
  });
});
