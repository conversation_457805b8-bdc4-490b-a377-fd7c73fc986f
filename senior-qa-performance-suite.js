// Professional API Performance Testing Suite
// Senior QA Engineer - Executive Reporting
import http from 'k6/http';
import { check, group, sleep } from 'k6';
import { Rate, Trend, Counter, Gauge } from 'k6/metrics';
import { htmlReport } from 'https://raw.githubusercontent.com/benc-uk/k6-reporter/main/dist/bundle.js';

// Professional Custom Metrics for Executive Reporting
let errorRate = new Rate('error_rate');
let apiResponseTime = new Trend('api_response_time');
let businessTransactionTime = new Trend('business_transaction_time');
let apiCallsPerSecond = new Rate('api_calls_per_second');
let activeUsers = new Gauge('active_users');

// Professional Load Testing Configuration
export let options = {
  stages: [
    // === BASELINE TESTING ===
    { duration: '2m', target: 10, name: 'Baseline Warm-up' },
    { duration: '5m', target: 10, name: 'Baseline Load' },
    
    // === LOAD TESTING ===
    { duration: '3m', target: 50, name: 'Load Test Ramp-up' },
    { duration: '10m', target: 50, name: 'Load Test Sustained' },
    
    // === STRESS TESTING ===
    { duration: '3m', target: 100, name: 'Stress Test Ramp-up' },
    { duration: '10m', target: 100, name: 'Stress Test Sustained' },
    
    // === SPIKE TESTING ===
    { duration: '1m', target: 200, name: 'Spike Test' },
    { duration: '2m', target: 200, name: 'Spike Test Hold' },
    
    // === RECOVERY TESTING ===
    { duration: '3m', target: 50, name: 'Recovery Test' },
    { duration: '2m', target: 0, name: 'Ramp Down' },
  ],
  
  // Professional SLA Thresholds for Executive Reporting
  thresholds: {
    // Overall Performance SLAs
    http_req_duration: ['p(95)<500', 'p(99)<1000'],
    http_req_failed: ['rate<0.01'],
    error_rate: ['rate<0.01'],
    
    // API-Specific SLAs
    'http_req_duration{endpoint:auth}': ['p(95)<200'],
    'http_req_duration{endpoint:users}': ['p(95)<300'],
    'http_req_duration{endpoint:search}': ['p(95)<400'],
    'http_req_duration{endpoint:upload}': ['p(95)<2000'],
    
    // Business Transaction SLAs
    business_transaction_time: ['p(95)<2000'],
    
    // Throughput SLAs
    http_reqs: ['rate>100'],
  },
};

// Professional Test Configuration
const config = {
  baseURL: 'https://your-api.com',
  authToken: '',
  testUsers: [
    { username: 'load_user_1', password: 'test123' },
    { username: 'load_user_2', password: 'test123' },
    { username: 'load_user_3', password: 'test123' },
  ],
  thinkTime: {
    min: 1,
    max: 3
  }
};

// Professional Setup - Authentication & Configuration
export function setup() {
  console.log('🚀 Starting Professional API Performance Test Suite');
  console.log('📊 Test configured for Executive Reporting');
  
  // Authenticate test user
  let authResponse = http.post(`${config.baseURL}/auth/login`, JSON.stringify({
    username: config.testUsers[0].username,
    password: config.testUsers[0].password
  }), {
    headers: { 'Content-Type': 'application/json' }
  });
  
  if (authResponse.status === 200) {
    config.authToken = authResponse.json().token;
    console.log('✅ Authentication successful');
  }
  
  return { authToken: config.authToken };
}

// Main Performance Test Suite
export default function(data) {
  let headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${data.authToken}`
  };
  
  // Track active users for executive reporting
  activeUsers.add(1);
  
  // === CRITICAL BUSINESS TRANSACTIONS ===
  group('🔐 Authentication Services', () => {
    let startTime = Date.now();
    
    let loginResponse = http.post(`${config.baseURL}/auth/login`, JSON.stringify({
      username: config.testUsers[Math.floor(Math.random() * config.testUsers.length)].username,
      password: 'test123'
    }), { 
      headers: { 'Content-Type': 'application/json' },
      tags: { endpoint: 'auth' }
    });
    
    // Professional validation checks
    let loginSuccess = check(loginResponse, {
      '✅ Login: Status 200': (r) => r.status === 200,
      '✅ Login: Response time < 200ms': (r) => r.timings.duration < 200,
      '✅ Login: Token received': (r) => r.json().hasOwnProperty('token'),
      '✅ Login: Token format valid': (r) => r.json().token && r.json().token.length > 20,
    });
    
    errorRate.add(!loginSuccess);
    apiResponseTime.add(loginResponse.timings.duration);
    apiCallsPerSecond.add(1);
    
    let transactionTime = Date.now() - startTime;
    businessTransactionTime.add(transactionTime);
  });
  
  group('👥 User Management Services', () => {
    let startTime = Date.now();
    
    // Get Users List - Critical Business Function
    let getUsersResponse = http.get(`${config.baseURL}/api/users?page=1&limit=20`, {
      headers: headers,
      tags: { endpoint: 'users' }
    });
    
    let getUsersSuccess = check(getUsersResponse, {
      '✅ Get Users: Status 200': (r) => r.status === 200,
      '✅ Get Users: Response time < 300ms': (r) => r.timings.duration < 300,
      '✅ Get Users: Valid response structure': (r) => r.json().hasOwnProperty('users'),
      '✅ Get Users: Pagination present': (r) => r.json().hasOwnProperty('pagination'),
      '✅ Get Users: Data integrity': (r) => Array.isArray(r.json().users),
    });
    
    errorRate.add(!getUsersSuccess);
    apiResponseTime.add(getUsersResponse.timings.duration);
    apiCallsPerSecond.add(1);
    
    // Create User - Business Critical Operation
    let createUserResponse = http.post(`${config.baseURL}/api/users`, JSON.stringify({
      name: `LoadTest_User_${Date.now()}`,
      email: `loadtest_${Date.now()}@example.com`,
      department: 'QA Testing',
      role: 'user'
    }), {
      headers: headers,
      tags: { endpoint: 'users' }
    });
    
    let createUserSuccess = check(createUserResponse, {
      '✅ Create User: Status 201': (r) => r.status === 201,
      '✅ Create User: Response time < 400ms': (r) => r.timings.duration < 400,
      '✅ Create User: ID returned': (r) => r.json().hasOwnProperty('id'),
      '✅ Create User: Data persisted': (r) => r.json().name.includes('LoadTest_User_'),
    });
    
    errorRate.add(!createUserSuccess);
    apiResponseTime.add(createUserResponse.timings.duration);
    apiCallsPerSecond.add(1);
    
    let transactionTime = Date.now() - startTime;
    businessTransactionTime.add(transactionTime);
  });
  
  group('🔍 Search & Analytics Services', () => {
    let searchResponse = http.get(`${config.baseURL}/api/search?q=test&type=users&page=1`, {
      headers: headers,
      tags: { endpoint: 'search' }
    });
    
    let searchSuccess = check(searchResponse, {
      '✅ Search: Status 200': (r) => r.status === 200,
      '✅ Search: Response time < 400ms': (r) => r.timings.duration < 400,
      '✅ Search: Results returned': (r) => r.json().hasOwnProperty('results'),
      '✅ Search: Performance acceptable': (r) => r.timings.duration < 500,
    });
    
    errorRate.add(!searchSuccess);
    apiResponseTime.add(searchResponse.timings.duration);
    apiCallsPerSecond.add(1);
  });
  
  group('📁 File Management Services', () => {
    let uploadResponse = http.post(`${config.baseURL}/api/upload`, {
      file: http.file('testfile.txt', 'QA Performance Test File Content', 'text/plain'),
      description: 'QA Load Testing File'
    }, {
      headers: {
        'Authorization': headers.Authorization
      },
      tags: { endpoint: 'upload' }
    });
    
    let uploadSuccess = check(uploadResponse, {
      '✅ Upload: Status 200': (r) => r.status === 200,
      '✅ Upload: Response time < 2000ms': (r) => r.timings.duration < 2000,
      '✅ Upload: File URL returned': (r) => r.json().hasOwnProperty('url'),
    });
    
    errorRate.add(!uploadSuccess);
    apiResponseTime.add(uploadResponse.timings.duration);
    apiCallsPerSecond.add(1);
  });
  
  group('🩺 Health & Monitoring Services', () => {
    let healthResponse = http.get(`${config.baseURL}/health`, {
      tags: { endpoint: 'health' }
    });
    
    let healthSuccess = check(healthResponse, {
      '✅ Health Check: Status 200': (r) => r.status === 200,
      '✅ Health Check: Response time < 100ms': (r) => r.timings.duration < 100,
      '✅ Health Check: Status OK': (r) => r.json().status === 'OK',
    });
    
    errorRate.add(!healthSuccess);
    apiResponseTime.add(healthResponse.timings.duration);
    apiCallsPerSecond.add(1);
  });
  
  // Professional think time simulation
  sleep(Math.random() * (config.thinkTime.max - config.thinkTime.min) + config.thinkTime.min);
}

// Professional Test Teardown
export function teardown(data) {
  console.log('📊 Performance Test Suite Completed');
  console.log('📈 Generating Executive Summary Report...');
}

// Executive Summary Report Generation
export function handleSummary(data) {
  return {
    'executive-performance-report.html': htmlReport(data),
    'performance-results.json': JSON.stringify(data, null, 2),
    'executive-summary.txt': generateExecutiveSummary(data),
  };
}

// Professional Executive Summary Generator
function generateExecutiveSummary(data) {
  const summary = `
=======================================================
    API PERFORMANCE TEST - EXECUTIVE SUMMARY
=======================================================

TEST OVERVIEW:
• Test Duration: ${Math.round(data.state.testRunDurationMs / 1000)}s
• Total Requests: ${data.metrics.http_reqs.count}
• Virtual Users: Peak ${Math.max(...data.metrics.vus.values.map(v => v.value))}
• Request Rate: ${data.metrics.http_reqs.rate.toFixed(2)} req/s

PERFORMANCE SUMMARY:
• Average Response Time: ${data.metrics.http_req_duration.avg.toFixed(2)}ms
• 95th Percentile: ${data.metrics.http_req_duration['p(95)'].toFixed(2)}ms
• 99th Percentile: ${data.metrics.http_req_duration['p(99)'].toFixed(2)}ms
• Success Rate: ${((1 - data.metrics.http_req_failed.rate) * 100).toFixed(2)}%

SLA COMPLIANCE:
• Response Time SLA (p95 < 500ms): ${data.metrics.http_req_duration['p(95)'] < 500 ? '✅ PASS' : '❌ FAIL'}
• Error Rate SLA (< 1%): ${data.metrics.http_req_failed.rate < 0.01 ? '✅ PASS' : '❌ FAIL'}
• Throughput SLA (> 100 req/s): ${data.metrics.http_reqs.rate > 100 ? '✅ PASS' : '❌ FAIL'}

RECOMMENDATIONS:
• System demonstrated ${data.metrics.http_req_failed.rate < 0.01 ? 'excellent' : 'concerning'} stability
• Performance ${data.metrics.http_req_duration['p(95)'] < 500 ? 'meets' : 'fails'} enterprise SLA requirements
• Capacity planning: System can handle ${Math.round(data.metrics.http_reqs.rate)} req/s sustained load

Generated by: Senior QA Engineer - Performance Testing Suite
Date: ${new Date().toISOString().split('T')[0]}
=======================================================
  `;
  
  return summary;
} 